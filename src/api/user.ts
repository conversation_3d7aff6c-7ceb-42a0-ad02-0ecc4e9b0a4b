import http from '@/http/requestWrapper'

/** 获取地址列表 */
export const getUserAddressListApi = () => {
  return http.get('/user/address')
}

/** 添加地址 */
export const addUserAddressApi = (data: API.User.AddressParams) => {
  return http.post('/user/address', { data })
}

/** 获取地址详情 */
export const getUserAddressDetailApi = (id: string) => {
  return http.get(`/user/address/${id}`)
}

/** 更新地址 */
export const updateUserAddressApi = (data: Partial<API.User.AddressParams> & { id: string }) => {
  const id = data.id
  delete data.id
  return http.put(`/user/address/${id}`, { data })
}

/** 删除地址 */
export const deleteUserAddressApi = (id: string) => {
  return http.delete(`/user/address/${id}`)
}

/** 设置默认地址 */
export const setDefaultUserAddressApi = (id: string) => {
  return http.patch(`/user/address/${id}/default`)
}

/** 获取默认地址 */
export const getDefaultUserAddressApi = () => {
  return http.get('/user/address/default')
}

/** 获取银行卡列表 */
export const getUserBankCardListApi = () => {
  return http.get('/user/bankcard')
}

/** 获取绑定银行卡短信验证码 */
export const getUserBankCardSmsCodeApi = (data: { card_uid: string; verify_code: string; request_id: string }) => {
  return http.post(`/user/bankcard/confirm`, { data })
}

/** 绑定银行卡 */
export const bindUserBankCardApi = (data: API.User.BankCardParams) => {
  return http.post('/user/bankcard', { data })
}
