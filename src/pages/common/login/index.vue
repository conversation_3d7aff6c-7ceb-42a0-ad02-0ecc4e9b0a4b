<route lang="json5" type="page">
{
  layout: 'theme', // 使用主题
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '登录'
  }
}
</route>

<template>
  <view class="login-container">
    <!-- 背景 -->
    <view class="header-bg" />

    <!-- 标题 -->
    <view class="login-header">
      <text class="login-title">欢迎登录花支优品</text>
    </view>

    <!-- 表单区域 -->
    <view class="login-form">
      <!-- 手机号输入区域 -->
      <view class="form-input-wrapper">
        <view class="form-prefix">
          <text class="country-code">+86</text>
        </view>
        <input
          v-model="phone"
          class="form-input pl-20rpx"
          type="number"
          placeholder="请输入手机号"
          :maxlength="11"
          placeholderStyle="color: #999"
        />
        <view v-if="phone" class="clear-icon" @tap="clearPhone">
          <text class="clear-text">×</text>
        </view>
      </view>

      <!-- 验证码输入区域 -->
      <view class="code-area">
        <view class="form-input-wrapper code-input-wrapper">
          <input
            v-model="verifyCode"
            class="form-input"
            type="number"
            placeholder="请输入验证码"
            :maxlength="6"
            placeholderStyle="color: #999"
          />
        </view>
        <view class="verify-btn" :class="[codeSending ? 'verify-btn-disabled' : '']" @tap="sendCode">
          <text>{{ countdown > 0 ? `${countdown}S后重新获取` : '获取验证码' }}</text>
        </view>
      </view>

      <!-- 隐私协议区域 -->
      <view class="privacy-area">
        <wd-checkbox v-model="privacyAgreed" shape="circle" checked-color="#ff5050" />
        <text class="privacy-text !ml--5rpx">
          已阅读并同意
          <text class="privacy-link" v-for="item in agreementList" :key="item.name" @tap="openAgreement(item)">
            {{ item.name }}
          </text>
        </text>
      </view>

      <!-- 登录按钮 -->
      <view class="login-btn" :class="[!canLogin ? 'login-btn-disabled' : '']" @tap="handleLogin">
        <text class="login-btn-text">登录</text>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { getCaptchaApi, loginApi, getAgreementApi } from '@/api'

// 表单数据
const phone = ref('')
const verifyCode = ref('')
const countdown = ref(0)
const codeSending = ref(false)
const isBack = ref(false)
const privacyAgreed = ref(false) // 默认不勾选协议

// 计算属性
const isPhoneValid = computed(() => {
  return /^1[3-9]\d{9}$/.test(phone.value)
})

const canLogin = computed(() => {
  return isPhoneValid.value && verifyCode.value.length === 6 && privacyAgreed.value
})

const agreementList = ref<Array<{ name: string; link: string }>>([])

onMounted(async () => {
  let [res] = await getAgreementApi({ type: 'P01' })
  agreementList.value = res?.data || []
})

// 清除手机号
function clearPhone() {
  phone.value = ''
}

// 发送验证码
function sendCode() {
  // 验证手机号
  if (!phone.value) {
    uni.showToast({
      title: '请输入手机号',
      icon: 'none'
    })
    return
  }

  if (!isPhoneValid.value) {
    uni.showToast({
      title: '请输入正确的手机号',
      icon: 'none'
    })
    return
  }

  if (codeSending.value) return

  codeSending.value = true
  countdown.value = 60
  isBack.value = true // 模拟已发送验证码的状态，显示返回按钮

  // 调用验证码接口
  getCaptchaApi({ phone: phone.value, type: 'login' })
    .then(([res, err]) => {
      if (err) {
        uni.showToast({
          title: err.message || '验证码发送失败',
          icon: 'none'
        })
        return
      }

      Utils.eventTracking({
        eventType: 'click',
        remark: '获取验证码'
      })
      uni.showToast({
        title: '验证码发送成功',
        icon: 'none'
      })

      // 开发环境下，可以自动填充验证码方便测试
      // #ifdef H5 || APP-PLUS
      setTimeout(() => {
        verifyCode.value = '234908'
      }, 500)
      // #endif
    })
    .finally(() => {
      // 倒计时
      const timer = setInterval(() => {
        countdown.value--
        if (countdown.value <= 0) {
          clearInterval(timer)
          codeSending.value = false
        }
      }, 1000)
    })
}

// 处理登录
function handleLogin() {
  if (!canLogin.value) return

  // 调用登录接口
  loginApi({
    phone: phone.value,
    code: verifyCode.value,
    source: 'app'
  }).then(() => {
    uni.showToast({
      title: '登录成功',
      icon: 'success'
    })

    uni.switchTab({
      url: '/pages/tab/home/<USER>',
      success: () => {
        console.log('成功跳转到首页')
      }
    })
  })
}

// 打开协议页面
function openAgreement(item: { name: string; link: string }) {
  // 打开用户协议页面
  uni.navigateTo({
    url: `/pages/common/webview/index?title=${item.name}&url=${item.link}`
  })
}
</script>

<style lang="scss" scoped>
.login-container {
  position: relative;
  box-sizing: border-box;
  min-height: 100vh;
  padding: 0;
  background-color: #fff;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 0;
  width: 100%;
  height: 624rpx;
  background-image: url('/static/images/login/bg.png');
  background-repeat: no-repeat;
  background-size: cover;
}

.login-header {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  padding: 250rpx 48rpx 150rpx;
}

.login-title {
  font-size: 40rpx;
  font-weight: 500;
  letter-spacing: 2rpx;
}

.login-form {
  position: relative;
  z-index: 1;
  margin: 0 48rpx;
}

.form-input-wrapper {
  display: flex;
  align-items: center;
  height: 96rpx;
  padding: 0 30rpx;
  margin-bottom: 30rpx;
  background-color: #f6f8fa;
  border-radius: 20rpx;

  :deep(.uni-input-wrapper) {
    height: 96rpx;
    line-height: 96rpx;
  }
}

.form-prefix {
  width: 60rpx;
  margin-right: 10rpx;
}

.country-code {
  font-size: 28rpx;
  color: #333;
}

.form-input {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
}

.clear-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  color: #999;
  background-color: #ddd;
  border-radius: 50%;
}

.clear-text {
  font-size: 24rpx;
  line-height: 1;
}

.code-area {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.code-input-wrapper {
  flex: 1;
  margin-right: 20rpx;
  margin-bottom: 0;
}

.verify-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 264rpx;
  height: 96rpx;
  font-size: 26rpx;
  color: #fff;
  background-color: #ff5050;
  border-radius: 20rpx;
}

.verify-btn-disabled {
  background-color: #ccc;
}

.privacy-area {
  display: flex;
  align-items: center;
  margin-top: 58rpx;
  margin-bottom: 85rpx;
}

.privacy-text {
  margin-left: 10rpx;
  font-size: 26rpx;
  color: #666;
}

.privacy-link {
  color: #ff5050;
}

.login-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 96rpx;
  margin-bottom: 50rpx;
  font-size: 32rpx;
  color: #fff;
  background-color: #ff5050;
  border-radius: 20rpx;
}

.login-btn-disabled {
  background-color: #ffb3b3;
}

.login-btn-text {
  font-weight: 500;
}
</style>
