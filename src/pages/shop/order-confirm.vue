<route lang="json5" type="page">
{
  layout: 'theme',
  style: {
    navigationBarTitleText: '确认订单',
    navigationStyle: 'default'
  }
}
</route>

<template>
  <view class="order-confirm box-border flex flex-col bg-gray-100 px-30rpx">
    <!-- 地址选择 -->
    <view class="address-section my-20rpx">
      <address-item :address="hasAddress ? address : null" @click="navigateToAddress" />
    </view>

    <!-- 商品信息 -->
    <view class="product-section my-20rpx rounded-13rpx bg-white p-30rpx">
      <view class="flex">
        <view
          class="mr-20rpx box-border h-180rpx w-180rpx overflow-hidden border-1rpx border-[#f5f5f5] rounded-22rpx border-solid p-3rpx"
        >
          <image :src="productInfo.image || ''" class="h-full w-full" mode="aspectFit" />
        </view>
        <view class="flex-1">
          <view class="line-clamp-2 text-32rpx text-[#222]">{{ productInfo.name || '' }}</view>
          <view class="mt-6rpx text-26rpx text-[#999]">{{ productInfo.sku_name || '' }}</view>
          <view class="mt-46rpx flex items-center justify-between">
            <view class="text-40rpx text-[var(--primary-color)] font-medium">¥{{ productInfo.price || 0 }}</view>
            <!-- 数量累加器 -->
            <view class="flex items-center">
              <view
                class="h-56rpx w-56rpx flex items-center justify-center rounded-8rpx"
                :class="[(productInfo.quantity || 0) <= 1 ? 'bg-[#F7F7F7]' : 'bg-[#F5F5F5]']"
                @click="decreaseQuantity"
              >
                <text :class="[(productInfo.quantity || 0) <= 1 ? 'text-[#ccc]' : 'text-gray-600']">-</text>
              </view>
              <view class="mx-25rpx text-center">
                <text class="text-28rpx">{{ productInfo.quantity || 0 }}</text>
              </view>
              <view
                class="h-56rpx w-56rpx flex items-center justify-center rounded-8rpx bg-[#F5F5F5]"
                @click="increaseQuantity"
              >
                <text class="text-gray-600">+</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 商品规格模块 -->
    <view class="product-detail-section my-20rpx rounded-18rpx bg-white px-30rpx">
      <view class="flex items-center justify-between py-35rpx border-b-1rpx-#efefef">
        <view class="text-26rpx text-[#333]">商品总价</view>
        <view class="text-26rpx text-[#333] font-medium">¥{{ productInfo.subtotal || 0 }}</view>
      </view>
      <view class="flex items-center justify-between py-35rpx border-b-1rpx-#efefef">
        <view class="text-26rpx text-[#333]">运费</view>
        <view class="text-26rpx text-[#333]">{{ deliveryFee > 0 ? '¥' + deliveryFee : '包邮' }}</view>
      </view>
      <!-- 这里是备注选项，点击可以打开备注弹窗 -->
      <view class="flex items-center justify-between py-35rpx">
        <view class="text-26rpx text-[#333]">备注</view>
        <view class="flex items-center" @click="showRemarkPopup = true">
          <view class="mt--4rpx text-26rpx text-[#999]">{{ remark || '添加备注' }}</view>
          <wd-icon name="arrow-right" size="26rpx" color="#999" />
        </view>
      </view>
    </view>

    <!-- 备注弹窗 -->
    <wd-popup v-model="showRemarkPopup" position="bottom" closable round :z-index="999">
      <view class="remark-popup box-border p-30rpx pb-50rpx">
        <view class="py-20rpx text-center text-32rpx font-medium">备注</view>
        <wd-textarea
          v-model="remarkInput"
          placeholder="请输入..."
          :maxlength="150"
          show-word-limit
          custom-class="rounded-16rpx mt-30rpx"
        />
        <view class="mt-30rpx">
          <view
            class="btn-primary h-80rpx flex items-center justify-center rounded-40rpx text-30rpx text-white"
            @click="confirmRemark"
          >
            提交
          </view>
        </view>
      </view>
    </wd-popup>

    <!-- 支付方式 -->
    <view class="payment-section my-20rpx rounded-18rpx bg-white p-30rpx">
      <view class="flex items-center justify-between py-15rpx" @click="navigateToBankCard">
        <view class="flex items-center">
          <view class="text-26rpx text-[#333]">银行卡</view>
        </view>
        <view class="flex items-center">
          <view v-if="bankCard" class="text-26rpx text-[#999]">{{ bankCard.number }}</view>
          <view v-else class="text-26rpx text-[#999]">未绑定银行卡</view>
          <wd-icon name="arrow-right" size="26rpx" color="#999" />
        </view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view
      class="fixed bottom-0 left-0 right-0 z-99 h-140rpx flex items-center justify-between border-t border-gray-200 bg-white px-30rpx"
    >
      <view class="flex items-baseline">
        <text class="text-26rpx">合计：</text>
        <text class="text-43rpx text-[var(--primary-color)]">¥</text>
        <text class="text-43rpx text-[var(--primary-color)] font-bold">{{ parseFloat(totalAmount.toFixed(2)) }}</text>
      </view>
      <view class="btn-primary w-300rpx text-30rpx" @click="submitOrder">提交订单</view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { getDefaultUserAddressApi } from '@/api'
import { onPageEvent } from '@/utils/event-channel'

// 商品信息
interface ProductInfo {
  id?: string
  name?: string
  sku_name?: string
  price?: number
  quantity?: number
  image?: string
  totalPrice?: number
  subtotal?: number
}

const productInfo = ref<ProductInfo>({})

// 获取上一个页面传递的商品信息
onPageEvent<ProductInfo>('getCurrentGoodsDetail', function (data) {
  console.log('getCurrentGoodsDetail', data)
  productInfo.value = data
})

// 地址信息
const address = ref({})
const hasAddress = ref(true)

getDefaultUserAddressApi()
  .unwrap()
  .then(res => {
    console.log('getDefaultUserAddress', res.data.address)
    address.value = res.data.address
    hasAddress.value = true
  })

// 银行卡信息
const bankCard = ref({
  id: '1',
  number: '1512 (中国建设银行)',
  type: '建设银行'
})

// 金额计算
const deliveryFee = ref(0)
const totalAmount = computed(() => {
  return (productInfo.value.totalPrice || 0) + deliveryFee.value
})

// 备注相关
const remark = ref('')
const remarkInput = ref('')
const showRemarkPopup = ref(false)

// 确认备注
const confirmRemark = () => {
  remark.value = remarkInput.value
  showRemarkPopup.value = false
}

// 导航到地址选择页面
const navigateToAddress = () => {
  console.log(`1 -->`, 1)
  uni.navigateTo({
    url: '/pages/user/address-list?from=order'
  })
}

// 导航到银行卡选择页面
const navigateToBankCard = () => {
  uni.navigateTo({
    url: '/pages/user/bank-card?from=order'
  })
}

// 增加商品数量
const increaseQuantity = () => {
  if (productInfo.value.quantity) {
    productInfo.value.quantity++
    updateTotalPrice()
  }
}

// 减少商品数量
const decreaseQuantity = () => {
  if (productInfo.value.quantity && productInfo.value.quantity > 1) {
    productInfo.value.quantity--
    updateTotalPrice()
  }
}

// 更新总价
const updateTotalPrice = () => {
  if (productInfo.value.price && productInfo.value.quantity) {
    productInfo.value.totalPrice = productInfo.value.price * productInfo.value.quantity
  }
}

// 提交订单
const submitOrder = () => {
  if (!hasAddress.value) {
    uni.showToast({
      title: '请选择收货地址',
      icon: 'none'
    })
    return
  }

  if (!bankCard.value) {
    uni.showToast({
      title: '请选择支付方式',
      icon: 'none'
    })
    return
  }

  // 跳转到支付页面
  uni.redirectTo({
    url: '/pages/shop/payment?productId=' + (productInfo.value.id || '')
  })
}

// 在组件卸载时移除事件监听
onUnmounted(() => {
  uni.$off('selectBankCard')
  uni.$off('selectAddress')
})

// 页面加载
onLoad((_options: any) => {
  // 监听银行卡选择事件
  listenForBankCardSelection()

  // 监听地址选择事件
  listenForAddressSelection()
})

// 监听银行卡选择
const listenForBankCardSelection = () => {
  // 移除之前可能存在的监听器，避免重复
  uni.$off('selectBankCard')

  // 添加监听器
  uni.$on('selectBankCard', (selectedCard: any) => {
    console.log('接收到选中的银行卡', selectedCard)
    if (selectedCard && selectedCard.id) {
      bankCard.value = selectedCard
    }
  })
}

// 监听地址选择
const listenForAddressSelection = () => {
  // 移除之前可能存在的监听器，避免重复
  uni.$off('selectAddress')

  // 添加监听器
  uni.$on('selectAddress', (selectedAddress: any) => {
    console.log('接收到选中的地址', selectedAddress)
    if (selectedAddress && selectedAddress.id) {
      address.value = selectedAddress
      hasAddress.value = true
    }
  })
}
</script>

<style lang="scss" scoped>
.order-confirm {
  padding-bottom: 120rpx;
}
</style>
