<route lang="json5" type="tab">
{
  layout: 'theme',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '购物车'
  }
}
</route>

<template>
  <view class="shop px-10rpx">
    <!-- 使用z-paging包裹整个页面内容 -->
    <z-paging
      ref="mainPaging"
      v-model="cartList"
      :fixed="true"
      :show-scrollbar="false"
      :safe-area-inset-bottom="true"
      :auto-show-back-to-top="true"
      :loading-more-enabled="false"
      :hide-empty-view="true"
      @query="queryCartList"
    >
      <!-- 顶部标题栏 -->
      <view class="top-bar top-0 z-10 flex items-center justify-between bg-[#F3F5F7] p-30rpx">
        <text class="text-32rpx font-bold">购物车</text>
        <view class="text-24rpx" @click="clearAllCart">全部清除</view>
      </view>

      <!-- z-paging空状态提示 -->
      <view class="h-500rpx" v-if="cartList.length == 0">
        <wd-status-tip image="content" tip="暂无内容" />
      </view>

      <!-- 购物车商品列表 -->
      <view class="mx-20rpx mt-20rpx">
        <view
          v-for="(item, index) in cartList"
          :key="item.id"
          class="relative rounded-16rpx bg-white p-(x-22rpx y-26rpx)"
          :class="index !== cartList.length - 1 ? 'mb-30rpx' : ''"
        >
          <!-- 删除按钮 -->
          <view class="absolute right-26rpx top-10rpx z-10" @click="handleRemoveCart(item)">
            <text class="i-uiw-delete text-24rpx text-[#CCCCCC]"></text>
          </view>

          <view class="flex">
            <!-- 商品图片 -->
            <view class="mr-20rpx">
              <wd-img :src="item.image" width="150rpx" height="150rpx" radius="8rpx" />
            </view>

            <!-- 商品信息 -->
            <view class="flex flex-1 flex-col">
              <view class="line-clamp-2 pr-40rpx text-24rpx font-medium">{{ item.name }}</view>
              <view
                class="mt-10rpx inline-block h-34rpx self-start rounded-4rpx rounded-8rpx bg-[#F2F2F2] px-10rpx text-20rpx leading-34rpx"
              >
                {{ item.sku_name }}
              </view>

              <!-- 数量控制 -->
              <view class="mr-10rpx mt-4rpx h-36rpx flex items-center self-end leading-36rpx">
                <view class="flex-center" @click="handleQuantityChange(item, -1)">
                  <text class="text-26rpx text-[#000]" :class="item.quantity <= 1 ? 'text-[#ccc]' : ''">-</text>
                </view>
                <view class="mx-10rpx min-w-70rpx flex-center rounded-8rpx bg-[#F7F7F7] px-20rpx">
                  <text class="text-20rpx">{{ item.quantity }}</text>
                </view>
                <view class="flex-center" @click="handleQuantityChange(item, 1)">
                  <text class="text-26rpx text-[#000]">+</text>
                </view>
              </view>

              <!-- 价格和立即购买 -->
              <view class="mt-22rpx flex items-center justify-between">
                <view class="text-28rpx text-primary font-bold">¥{{ item.price }}</view>

                <!-- 立即购买按钮 -->
                <wd-button
                  class="btn-primary !h-52rpx !min-w-auto !w-138rpx !rounded-10rpx !text-24rpx"
                  @click="buyNow(item)"
                >
                  立即购买
                </wd-button>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 商品推荐 -->
      <view class="my-30rpx px-20rpx">
        <goods-recommendation ref="goodsRecommendationRef" @goods-add-cart="handleGoodsAddCart"></goods-recommendation>
      </view>
    </z-paging>
  </view>

  <!-- !! 底部结算栏 暂时隐藏 -->
  <view
    v-if="0"
    class="fixed bottom-[calc(var(--window-bottom)-1rpx)] left-0 right-0 z-999 h-100rpx flex items-center bg-white px-30rpx"
  >
    <view class="flex items-center" @click="toggleSelectAll">
      <text
        :class="isAllSelected ? 'i-uiw-check-o text-[#FF3C29]' : 'i-uiw-circle-o text-[#CCCCCC]'"
        class="mr-10rpx text-40rpx"
      ></text>
      <text class="text-28rpx">全选</text>
    </view>

    <view class="flex flex-1 items-center justify-end">
      <view class="mr-30rpx">
        <text class="text-26rpx text-[#333333]">合计：</text>
        <text class="text-32rpx text-[#FF3C29] font-bold">¥{{ totalPrice }}</text>
      </view>

      <view
        class="h-70rpx flex-center rounded-35rpx px-40rpx"
        :class="selectedCount > 0 ? 'bg-[#FF3C29] text-white' : 'bg-[#CCCCCC] text-[#FFFFFF]'"
        @click="checkout"
      >
        <text class="text-28rpx">结算({{ selectedCount }})</text>
      </view>
    </view>
  </view>

  <wd-message-box></wd-message-box>
</template>

<script setup lang="ts">
import { useCart } from '@/hooks/useCart'
import { useMessage } from 'wot-design-uni'
import { debounce } from '@/utils'

const message = useMessage()

defineOptions({
  name: 'CartPage'
})

const { cartList, fetchCartList, removeFromCart, clearCart, updateCartQuantity, loading } = useCart()

const goodsRecommendationRef = ref(null)

// z-paging组件引用
const mainPaging = ref(null)

// 查询购物车列表 - z-paging @query事件会触发此方法
const queryCartList = async (pageNo: number, pageSize: number) => {
  try {
    // 重新获取购物车列表
    await fetchCartList()

    // 完成加载
    mainPaging.value.complete(cartList.value)
  } catch (error) {
    console.error('加载购物车数据失败', error)
    mainPaging.value.complete(false)
  }
}

// 计算属性：是否全选
const isAllSelected = computed(() => {
  return cartList.value.length > 0 && cartList.value.every(item => item.selected)
})

// 计算属性：选中的商品数量
const selectedCount = computed(() => {
  return cartList.value.filter(item => item.selected).length
})

// 计算属性：总价
const totalPrice = computed(() => {
  return cartList.value
    .filter(item => item.selected)
    .reduce((total, item) => total + Number(item.price) * item.quantity, 0)
    .toFixed(2)
})

// 切换商品选中状态
const toggleSelect = item => {
  item.selected = !item.selected
}

// 切换全选状态
const toggleSelectAll = () => {
  const newStatus = !isAllSelected.value
  cartList.value.forEach(item => {
    item.selected = newStatus
  })
}

// 防抖处理数量更新的API调用
const debouncedUpdateQuantity = debounce((id: string, quantity: number) => {
  updateCartQuantity(id, quantity)
}, 300)

// 处理商品数量更新
const handleQuantityChange = (item, quantity) => {
  if (item.quantity + quantity <= 1 || item.quantity + quantity >= item.stock) {
    return
  }

  // 立即更新UI
  item.quantity += quantity

  // 使用防抖函数延迟调用API
  debouncedUpdateQuantity(item.cart_id, item.quantity)
}

// 处理删除购物车商品
const handleRemoveCart = item => {
  message
    .confirm({
      title: '提示',
      msg: '确定要删除该商品吗？',
      zIndex: 99999
    })
    .then(() => {
      removeFromCart([item.cart_id])
    })
}

// 立即购买
const buyNow = item => {
  uni.navigateTo({
    url: `/pages/shop/order-confirm?productId=${item.sku_id}`,
    event: {},
    success: res => {
      res.eventChannel.emit('getCurrentGoodsDetail', item)
    }
  })
}

// 清空购物车
const clearAllCart = () => {
  if (cartList.value.length === 0) return

  message
    .confirm({
      title: '提示',
      msg: '确定要清空购物车吗？',
      zIndex: 99999
    })
    .then(() => {
      clearCart()
    })
}

// 去结算
const checkout = () => {
  if (selectedCount.value === 0) return

  uni.navigateTo({
    url: '/pages/shop/checkout'
  })
}

// 处理商品加入购物车事件
const handleGoodsAddCart = (goods: any) => {
  mainPaging.value.refresh()
}

onShow(() => {
  nextTick(() => {
    mainPaging.value.refresh()
  })
})
</script>

<style lang="scss" scoped>
.top-bar {
  /* #ifdef APP-PLUS */
  margin-top: calc(var(--status-bar-height) + 30rpx);
  /* #endif */
}
</style>
