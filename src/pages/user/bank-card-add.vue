<route lang="json5" type="page">
{
  layout: 'theme',
  style: {
    navigationBarTitleText: '银行卡绑定',
    navigationStyle: 'default'
  }
}
</route>

<template>
  <view class="add-bank-card-page min-h-100vh bg-[#F8F8F8]">
    <view class="px-30rpx py-30rpx text-28rpx text-[#222]">
      <!-- 第一层：基本信息区域 -->
      <view class="mb-20rpx rounded-20rpx bg-white px-30rpx">
        <!-- 持卡人 -->
        <view class="form-item box-border flex items-center border-b border-[#EFEFEF] py-30rpx">
          <view class="label w-172rpx">持卡人</view>
          <input
            class="flex-1"
            type="text"
            v-model="formData.cardHolder"
            placeholder="请输入持卡人姓名"
            placeholder-class="text-[#999] text-28rpx"
          />
        </view>

        <!-- 身份证号 -->
        <view class="form-item flex items-center border-b border-[#EFEFEF] py-30rpx">
          <view class="label w-172rpx">身份证号</view>
          <input
            class="flex-1"
            type="idcard"
            v-model="formData.idCard"
            placeholder="请输入身份证号"
            placeholder-class="text-[#999] text-28rpx"
          />
        </view>

        <!-- 银行卡号 -->
        <view class="form-item flex items-center py-30rpx">
          <view class="label w-172rpx">银行卡号</view>
          <input
            class="flex-1"
            type="text"
            v-model="formData.cardNo"
            placeholder="请输入银行卡号"
            placeholder-class="text-[#999] text-28rpx"
            @input="handleCardNoInput"
          />
        </view>
      </view>

      <!-- 第二层：验证信息区域 -->
      <view class="mb-50rpx rounded-20rpx bg-white p-30rpx">
        <!-- 预留手机号 -->
        <view class="form-item flex items-center border-b border-[#EFEFEF] py-30rpx">
          <view class="label w-172rpx">预留手机号</view>
          <input
            class="flex-1"
            type="number"
            :maxlength="11"
            v-model="formData.phone"
            placeholder="请输入银行预留手机号"
            placeholder-class="text-[#999] text-28rpx"
          />
        </view>

        <!-- 验证码 -->
        <view class="form-item flex items-center py-30rpx">
          <view class="label w-172rpx">验证码</view>
          <input
            class="flex-1"
            type="number"
            :maxlength="6"
            v-model="formData.verifyCode"
            placeholder="请输入验证码"
            placeholder-class="text-[#999] text-28rpx"
          />
          <view
            class="verify-btn ml-20rpx h-auto w-auto rounded-8rpx px-30rpx py-15rpx !rounded-12rpx !text-26rpx"
            :class="canSendCode ? 'btn-primary text-white' : 'btn-primary-disabled'"
            @click="sendVerifyCode"
          >
            {{ codeText }}
          </view>
        </view>
      </view>

      <!-- 第三层：下一步按钮 -->
      <view class="mt-146rpx px-30rpx">
        <button
          class="h-90rpx w-full flex-center rounded-full"
          :class="isFormValid ? 'btn-primary' : 'btn-primary-disabled'"
          :disabled="!isFormValid"
          @click="handleSubmit"
        >
          下一步
        </button>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'

// 表单数据
const formData = reactive({
  cardHolder: '',
  idCard: '',
  cardNo: '',
  phone: '',
  verifyCode: '',
  isDefault: false
})

// 验证码相关
const countdown = ref(0)
const codeText = computed(() => {
  return countdown.value > 0 ? `已发送(${countdown.value}s)` : '获取验证码'
})
const canSendCode = computed(() => {
  return /^1\d{10}$/.test(formData.phone) && countdown.value === 0
})

// 处理银行卡号输入，格式化显示
const handleCardNoInput = (e: any) => {
  // 移除所有空格
  let value = e.detail.value.replace(/\s/g, '')

  // 每4位添加一个空格，提高可读性
  value = value.replace(/(\d{4})(?=\d)/g, '$1 ')

  // 设置格式化后的值
  formData.cardNo = value
}

// 发送验证码
const sendVerifyCode = () => {
  if (!canSendCode.value) return

  // 验证手机号
  if (!/^1\d{10}$/.test(formData.phone)) {
    uni.showToast({
      title: '请输入正确的手机号',
      icon: 'none'
    })
    return
  }

  // 开始倒计时
  countdown.value = 60
  const timer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(timer)
    }
  }, 1000)

  // 模拟发送验证码
  uni.showToast({
    title: '验证码已发送',
    icon: 'success'
  })
}

// 表单验证
const isFormValid = computed(() => {
  const idCardReg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
  return (
    formData.cardHolder.trim() !== '' &&
    idCardReg.test(formData.idCard) &&
    formData.cardNo.replace(/\s/g, '').length >= 16 &&
    /^1\d{10}$/.test(formData.phone) &&
    formData.verifyCode.length >= 4
  )
})

// 提交表单
const handleSubmit = () => {
  if (!isFormValid.value) {
    uni.showToast({
      title: '请填写完整信息',
      icon: 'none'
    })
    return
  }

  // 显示加载中
  uni.showLoading({
    title: '验证中...'
  })

  // 模拟提交请求
  setTimeout(() => {
    uni.hideLoading()
    uni.showToast({
      title: '验证成功',
      icon: 'success'
    })

    // 返回上一页
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }, 1000)
}
</script>

<style>
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
