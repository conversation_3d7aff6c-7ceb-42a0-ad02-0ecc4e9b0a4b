<route lang="json5" type="page">
{
  layout: 'theme',
  style: {
    navigationBarTitleText: '银行卡',
    navigationStyle: 'default'
  }
}
</route>

<template>
  <view class="bank-card-page">
    <!-- 页面内容 -->
    <view class="overflow-hidden px-30rpx pb-100rpx">
      <!-- 银行卡列表 -->
      <block v-if="cardList.length > 0">
        <view
          v-for="(card, index) in cardList"
          :key="index"
          class="bank-card-item mb-30rpx mt-30rpx h-242rpx overflow-hidden rounded-20rpx"
          @click="selectCard(card)"
          :style="{ background: 'linear-gradient(to right, #FF5D5D 0%, #FF3C29 100%)' }"
        >
          <!-- 卡片内容 - 分为上下两部分 -->
          <view class="h-full flex flex-col">
            <!-- 上半部分 - 卡片信息 -->
            <view class="flex-1 p-30rpx">
              <!-- 第一行: 银行图标、名称和换绑卡标签 -->
              <view class="flex items-center justify-between">
                <view class="flex items-center">
                  <!-- 银行图标和名称 -->
                  <view class="mr-15rpx h-50rpx w-50rpx flex-center rounded-full bg-white">
                    <image
                      :src="card.bankIcon || getBankIcon(card.bankName)"
                      class="h-35rpx w-35rpx"
                      mode="aspectFit"
                    ></image>
                  </view>
                  <view>
                    <text class="text-30rpx text-white font-medium">{{ card.bankName }}</text>
                    <view class="mt-4rpx text-22rpx text-white opacity-80">
                      {{ card.cardType === 'credit' ? '信用卡' : '储蓄卡' }}
                    </view>
                  </view>
                </view>

                <!-- 换绑卡标签 -->
                <view class="rounded-8rpx bg-white bg-opacity-20 px-16rpx py-4rpx">
                  <text class="text-22rpx text-white">{{ card.isDefault ? '默认' : '换绑卡' }}</text>
                </view>
              </view>
            </view>

            <!-- 下半部分 - 卡号 -->
            <view class="mt--60rpx flex flex-1 justify-between px-20rpx tracking-wider">
              <view class="w-[22%] flex items-center justify-center">
                <text class="text-24rpx text-[#FBAAAC] tracking-12rpx">••••</text>
              </view>
              <view class="w-[22%] flex items-center justify-center">
                <text class="text-24rpx text-[#FBAAAC] tracking-12rpx">••••</text>
              </view>
              <view class="w-[22%] flex items-center justify-center">
                <text class="text-24rpx text-[#FBAAAC] tracking-12rpx">••••</text>
              </view>
              <view class="w-[22%] flex items-center justify-center">
                <text class="text-56rpx text-[#FEFEFE]">{{ formatCardNo(card.cardNo) }}</text>
              </view>
            </view>
          </view>
        </view>
      </block>

      <!-- 空状态 -->
      <view v-else class="empty-state flex-col-center py-100rpx">
        <image
          src="https://cdn.jsdelivr.net/gh/eric-leo/static/common/images/empty.png"
          class="mb-30rpx h-200rpx w-200rpx"
          mode="aspectFit"
        ></image>
        <text class="text-28rpx text-[#999]">暂未绑定银行卡</text>
        <text class="mt-10rpx text-26rpx text-[#BBBBBB]">添加银行卡可以支付、提现等功能</text>
      </view>
    </view>

    <!-- 底部添加按钮 -->
    <view class="fixed bottom-0 left-0 right-0 bg-white p-30rpx pb-safe">
      <wd-button
        icon="/static/images/user/add-card-icon.png"
        block
        custom-class="!bg-gradient-to-r from-[#FF5D5D] to-[#FF3C29] text-white rounded-full !h-90rpx leading-100% flex-center mb-30rpx"
        @click="navigateToAddCard"
      >
        添加银行卡
      </wd-button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'

// 获取银行图标
const getBankIcon = (bankName: string) => {
  // 银行图标映射表 - 使用网络图片代替
  const bankIcons: Record<string, string> = {
    中国工商银行: 'https://cdn.jsdelivr.net/gh/eric-leo/static/common/images/bank-icbc.png',
    中国农业银行: 'https://cdn.jsdelivr.net/gh/eric-leo/static/common/images/bank-abc.png',
    中国银行: 'https://cdn.jsdelivr.net/gh/eric-leo/static/common/images/bank-boc.png',
    中国建设银行: 'https://cdn.jsdelivr.net/gh/eric-leo/static/common/images/bank-ccb.png',
    交通银行: 'https://cdn.jsdelivr.net/gh/eric-leo/static/common/images/bank-bocom.png',
    招商银行: 'https://cdn.jsdelivr.net/gh/eric-leo/static/common/images/bank-cmb.png',
    中信银行: 'https://cdn.jsdelivr.net/gh/eric-leo/static/common/images/bank-citic.png',
    浦发银行: 'https://cdn.jsdelivr.net/gh/eric-leo/static/common/images/bank-spdb.png',
    兴业银行: 'https://cdn.jsdelivr.net/gh/eric-leo/static/common/images/bank-cib.png',
    中国光大银行: 'https://cdn.jsdelivr.net/gh/eric-leo/static/common/images/bank-ceb.png'
  }

  // 返回对应的图标URL或默认图标
  return bankIcons[bankName] || 'https://cdn.jsdelivr.net/gh/eric-leo/static/common/images/bank-icon.png'
}

// 银行卡列表
const cardList = ref([
  // 示例数据，实际应从API获取
  // {
  //   id: '1',
  //   bankName: '中国建设银行',
  //   cardNo: '6217 0000 0000 0000 000',
  //   bankIcon: 'https://cdn.jsdelivr.net/gh/eric-leo/static/common/images/bank-ccb.png',
  //   isDefault: true
  // }
])

// 格式化卡号，只显示后四位
const formatCardNo = (cardNo: string) => {
  if (!cardNo) return ''

  // 移除所有空格
  const cleanCardNo = cardNo.replace(/\s/g, '')
  if (cleanCardNo.length <= 4) return cleanCardNo

  // 获取后四位，用于卡片显示
  const lastFourDigits = cleanCardNo.slice(-4)

  // 根据设计图，直接显示最后4位数字，不显示前面的点或星号
  return lastFourDigits
}

// 选择银行卡
const selectCard = (card: any) => {
  // 如果是从订单页面跳转来的，则需要返回并传值
  const pages = getCurrentPages()
  const prevPage = pages[pages.length - 2]

  // 获取路由参数，检查是否来自订单页面
  const routeParams = uni.getLaunchOptionsSync().query || {}
  const isFromOrder =
    routeParams.from === 'order' || (prevPage && prevPage.route && prevPage.route.includes('order-confirm'))

  if (isFromOrder || (prevPage && prevPage.route && prevPage.route.includes('order-confirm'))) {
    // 返回上一页
    uni.navigateBack()

    // 构建要传递的银行卡数据
    const bankCardData = {
      id: card.id,
      number: `${card.cardNo.slice(-4)} (${card.bankName})`,
      type: card.bankName
    }

    // 通过事件总线传值
    setTimeout(() => {
      uni.$emit('selectBankCard', bankCardData)
    }, 100)

    // 显示提示
    uni.showToast({
      title: '已选择银行卡',
      icon: 'success',
      duration: 1500
    })
  }
}

// 跳转到添加银行卡页面
const navigateToAddCard = () => {
  uni.navigateTo({
    url: '/pages/user/bank-card-add'
  })
}

// 获取银行卡列表
const fetchCardList = () => {
  // 模拟接口请求
  setTimeout(() => {
    cardList.value = [
      {
        id: '1',
        bankName: '中国工商银行',
        cardNo: '6222 0000 0000 0000 8909',
        cardType: 'deposit', // 储蓄卡
        isDefault: true
      },
      {
        id: '2',
        bankName: '招商银行',
        cardNo: '5187 0000 0000 0000 3344',
        cardType: 'credit', // 信用卡
        isDefault: false
      },
      {
        id: '3',
        bankName: '中国建设银行',
        cardNo: '6217 0000 0000 0000 1234',
        cardType: 'deposit', // 储蓄卡
        isDefault: false
      }
    ]
  }, 500)
}

onMounted(() => {
  fetchCardList()
})
</script>

<style>
/* 可以添加页面特有的样式 */
</style>
