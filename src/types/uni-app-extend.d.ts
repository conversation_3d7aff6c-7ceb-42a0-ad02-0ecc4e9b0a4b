// 扩展UniApp类型定义，添加PATCH方法支持
declare namespace UniApp {
  interface RequestOptions {
    method?: 'OPTIONS' | 'GET' | 'HEAD' | 'POST' | 'PUT' | 'DELETE' | 'TRACE' | 'CONNECT' | 'PATCH'
  }
}

// 定义事件通道接口
interface EventChannel {
  on<T = any>(eventName: string, callback: (data: T) => void): void
  emit(eventName: string, data?: any): void
}

// 扩展Vue组件实例类型定义
declare module 'vue' {
  interface ComponentPublicInstance {
    getOpenerEventChannel(): EventChannel
  }

  // 扩展ComponentInternalInstance类型
  interface ComponentInternalInstance {
    proxy: ComponentPublicInstance & {
      getOpenerEventChannel(): EventChannel
    }
  }
}

// 扩展全局类型
declare global {
  interface Window {
    getOpenerEventChannel(): EventChannel
  }

  // 添加全局函数声明
  function getOpenerEventChannel(): EventChannel
}
