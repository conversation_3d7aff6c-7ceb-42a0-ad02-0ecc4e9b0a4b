/**
 * 事件通道工具函数
 * 用于处理页面间的事件通信
 */

// 定义事件通道接口
interface EventChannel {
  on<T = any>(eventName: string, callback: (data: T) => void): void
  emit(eventName: string, data?: any): void
}

/**
 * 获取页面间事件通道
 * 这个函数封装了获取事件通道的逻辑，避免直接使用类型断言
 */
export function getPageEventChannel(): EventChannel | null {
  try {
    // 在uni-app中，getOpenerEventChannel是全局可用的
    // 但TypeScript无法正确识别，所以我们通过window对象来访问
    if (typeof window !== 'undefined' && 'getOpenerEventChannel' in window) {
      return (window as any).getOpenerEventChannel()
    }
    
    // 如果window不可用，尝试通过getCurrentInstance获取
    const { getCurrentInstance } = require('vue')
    const instance = getCurrentInstance()
    if (instance && instance.proxy && 'getOpenerEventChannel' in instance.proxy) {
      return (instance.proxy as any).getOpenerEventChannel()
    }
    
    return null
  } catch (error) {
    console.warn('获取事件通道失败:', error)
    return null
  }
}

/**
 * 监听页面事件
 * @param eventName 事件名称
 * @param callback 回调函数
 */
export function onPageEvent<T = any>(eventName: string, callback: (data: T) => void): void {
  const eventChannel = getPageEventChannel()
  if (eventChannel) {
    eventChannel.on<T>(eventName, callback)
  } else {
    console.warn(`无法获取事件通道，无法监听事件: ${eventName}`)
  }
}

/**
 * 发送页面事件
 * @param eventName 事件名称
 * @param data 事件数据
 */
export function emitPageEvent(eventName: string, data?: any): void {
  const eventChannel = getPageEventChannel()
  if (eventChannel) {
    eventChannel.emit(eventName, data)
  } else {
    console.warn(`无法获取事件通道，无法发送事件: ${eventName}`)
  }
}
