// 模块统一导出
export * from './qs'
export * from './router'
export * from './log'
export * from './assets'
export * from './global'
export * from './event-channel'

// 防抖函数
type Fn = (...args: any[]) => any
export function debounce<T extends Fn>(fn: T, delay = 300) {
  let timer: ReturnType<typeof setTimeout> | null = null
  return function (this: any, ...args: Parameters<T>) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  } as T
}

// 节流函数
/**
 * throttle(fn, waitOrAsync)
 * @param fn 目标函数
 * @param waitOrAsync number: 间隔时间(ms) | true: 等待异步完成
 */
export function throttle<T extends (...args: any[]) => any>(fn: T, waitOrAsync: number | true): T {
  if (waitOrAsync === true) {
    let pending = false
    return async function (this: any, ...args: Parameters<T>) {
      if (pending) return
      pending = true
      try {
        await fn.apply(this, args)
      } finally {
        pending = false
      }
    } as T
  } else {
    let last = 0
    return function (this: any, ...args: Parameters<T>) {
      const now = Date.now()
      if (now - last >= waitOrAsync) {
        last = now
        fn.apply(this, args)
      }
    } as T
  }
}
